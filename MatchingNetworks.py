import random

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from scipy.io import loadmat
from sklearn.model_selection import train_test_split
from torch import optim
from torch.utils.data import DataLoader
from data import CustomDataset
from data import AddGaussianNoise
import torchvision.transforms as transforms
from pdw import Know
from visdom import Visdom
from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay
import matplotlib.pyplot as plt
from tqdm import tqdm

def set_seed(seed):
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


class ResidualBlock(nn.Module):
    def __init__(self, in_channels):
        super(ResidualBlock, self).__init__()
        self.conv1 = nn.Conv1d(in_channels, in_channels * 2, kernel_size=1, stride=1, padding=0)
        self.bn1 = nn.BatchNorm1d(in_channels * 2)
        self.conv2 = nn.Conv1d(in_channels * 2, in_channels, kernel_size=1, stride=1, padding=0)
        self.bn2 = nn.BatchNorm1d(in_channels)

    def forward(self, x):
        residual = x
        out = F.relu(self.bn1(self.conv1(x)))
        out = self.bn2(self.conv2(out))
        out += residual
        out = F.relu(out)
        return out


class AttentionLSTM(nn.Module):
    """LSTM with attention mechanism for processing support set"""
    def __init__(self, input_size, hidden_size):
        super(AttentionLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.lstm = nn.LSTM(input_size, hidden_size, batch_first=True)
        self.attention = nn.Linear(hidden_size, 1)
        
    def forward(self, x):
        # x shape: (batch_size, seq_len, input_size)
        lstm_out, _ = self.lstm(x)  # (batch_size, seq_len, hidden_size)
        
        # Compute attention weights
        attention_weights = F.softmax(self.attention(lstm_out), dim=1)  # (batch_size, seq_len, 1)
        
        # Apply attention
        attended_output = torch.sum(attention_weights * lstm_out, dim=1)  # (batch_size, hidden_size)
        
        return attended_output


class MatchingNetwork(nn.Module):
    def __init__(self, input_size, hidden_size, output_size, nhead=8, num_layers=2, seq_len=8):
        super(MatchingNetwork, self).__init__()
        
        # Encoder network (same as PrototypicalNetwork for fair comparison)
        self.conv1 = nn.Conv1d(1, input_size, kernel_size=1, stride=1)
        self.bn1 = nn.BatchNorm1d(input_size)
        
        # 添加残差块
        self.residual_blocks = nn.Sequential(
            ResidualBlock(input_size),
            ResidualBlock(input_size),
            ResidualBlock(input_size)
        )
        
        self.seq_len = seq_len
        encoder_layer = nn.TransformerEncoderLayer(d_model=input_size, nhead=nhead, batch_first=True)
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        self.fc1 = nn.Linear(seq_len * input_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, hidden_size)
        self.fc3 = nn.Linear(hidden_size, hidden_size // 4)
        self.fc4 = nn.Linear(hidden_size // 4, hidden_size // 2)
        self.fc5 = nn.Linear(hidden_size // 2, hidden_size // 2)
        self.fc6 = nn.Linear(hidden_size // 2, output_size)
        self.dropout = nn.Dropout(0.5)
        
        # Matching Networks specific components
        # Support set processing with attention
        self.support_lstm = AttentionLSTM(output_size, output_size)
        
        # Query set processing with attention  
        self.query_lstm = AttentionLSTM(output_size, output_size)
        
        # Attention mechanism for matching
        self.attention_fc = nn.Linear(output_size * 2, 1)
        
    def encode(self, x):
        """Shared encoder for both support and query sets"""
        x = x.unsqueeze(1)
        x = F.relu(self.conv1(x))
        x = F.relu(self.bn1(x))
        x = self.residual_blocks(x)
        x = x.permute(1, 0, 2)  # Transformer要求的输入格式为 (seq_len, batch_size, input_size)
        
        # 通过Transformer提取特征
        x = self.transformer_encoder(x)
        
        # 恢复形状 (batch_size, seq_len * input_size)，适用于全连接层
        x = x.permute(1, 0, 2).contiguous()  # 重新调整为 (batch_size, seq_len, input_size)
        x = x.view(x.size(0), -1)  # 展平操作，使其适用于全连接层
        
        x = F.relu(self.fc1(x))
        x = F.relu(self.fc2(x))
        x = F.relu(self.fc3(x))
        x = self.dropout(x)
        x = F.relu(self.fc4(x))
        x = F.relu(self.fc5(x))
        x = self.fc6(x)
        
        return x
    
    def forward(self, support_set, support_labels, query_set, num_classes, num_support):
        """
        Forward pass for Matching Networks
        
        Args:
            support_set: Support set samples
            support_labels: Support set labels  
            query_set: Query set samples
            num_classes: Number of classes
            num_support: Number of support samples per class
        """
        # Encode support and query sets
        support_embeddings = self.encode(support_set)  # (num_classes * num_support, embedding_dim)
        query_embeddings = self.encode(query_set)      # (num_query, embedding_dim)
        
        # Reshape support embeddings by class
        support_embeddings = support_embeddings.view(num_classes, num_support, -1)  # (num_classes, num_support, embedding_dim)
        
        # Process support set with attention LSTM for each class
        support_features = []
        for i in range(num_classes):
            class_support = support_embeddings[i].unsqueeze(0)  # (1, num_support, embedding_dim)
            class_feature = self.support_lstm(class_support)    # (1, embedding_dim)
            support_features.append(class_feature)
        
        support_features = torch.cat(support_features, dim=0)  # (num_classes, embedding_dim)
        
        # Process query set with attention (treating each query as a sequence of length 1)
        query_features = query_embeddings.unsqueeze(1)  # (num_query, 1, embedding_dim)
        query_features = self.query_lstm(query_features)  # (num_query, embedding_dim)
        
        # Compute attention-based matching scores
        num_query = query_features.size(0)
        logits = torch.zeros(num_query, num_classes).to(query_features.device)
        
        for i in range(num_query):
            query_feature = query_features[i].unsqueeze(0)  # (1, embedding_dim)
            
            for j in range(num_classes):
                support_feature = support_features[j].unsqueeze(0)  # (1, embedding_dim)
                
                # Concatenate query and support features
                combined = torch.cat([query_feature, support_feature], dim=1)  # (1, 2 * embedding_dim)
                
                # Compute attention score
                attention_score = self.attention_fc(combined)  # (1, 1)
                logits[i, j] = attention_score.squeeze()
        
        return logits


def matching_loss(logits, query_labels):
    """
    Matching Networks loss function
    
    Args:
        logits: Predicted logits (num_query, num_classes)
        query_labels: True labels for query set
    """
    return F.cross_entropy(logits, query_labels)


def process_batch_matching(matching_net, batch, device, num_classes, num_support):
    """Process batch for Matching Networks"""
    support_set = batch[0][:num_classes * num_support].to(device)
    support_labels = batch[1][:num_classes * num_support].to(device)
    num_query = batch[0].size(0) - num_classes * num_support
    query_set = batch[0][num_query:].to(device)
    query_labels = batch[1][num_query:].to(device)
    
    # Forward pass through Matching Network
    logits = matching_net(support_set, support_labels, query_set, num_classes, num_support)
    
    return logits, query_labels


def evaluate_matching_network(matching_net, test_loader, device, num_classes, num_support):
    """Evaluate Matching Network on test set"""
    matching_net.eval()
    correct = 0
    total = 0
    
    with torch.no_grad():
        for batch in test_loader:
            logits, query_labels = process_batch_matching(matching_net, batch, device, num_classes, num_support)
            
            _, predicted = torch.max(logits, dim=1)
            total += query_labels.size(0)
            correct += (predicted == query_labels).sum().item()
    
    accuracy = 100 * correct / total
    return accuracy


# 主函数
def main():
    seed = 42
    set_seed(seed)
    
    device = torch.device('cuda')
    
    # 数据集参数 (same as PrototypicalNetwork)
    num_classes = 10
    num_support = 10
    num_query = 15
    
    dictdata = loadmat("traindata_5dB.mat")
    dataload = dictdata['traindata']
    
    # 网络参数 (same as PrototypicalNetwork for fair comparison)
    input_size = 8
    hidden_size = 256
    output_size = 64
    
    # 创建Matching Network实例
    matching_net = MatchingNetwork(input_size, hidden_size, output_size)
    matching_net.to(device)
    optimizer = optim.Adam(matching_net.parameters(), lr=0.001)
    
    # 创建数据加载器 (same as PrototypicalNetwork)
    dataset = CustomDataset(dataload, noisy=False)
    
    train_datatest, test_datatest = train_test_split(dataset, test_size=0.9, random_state=42)
    train_datatest = DataLoader(train_datatest, batch_size=num_classes * (num_support + num_query), shuffle=True)
    test_datatest = DataLoader(test_datatest, batch_size=num_classes * (num_support + num_query), shuffle=True)
    
    x, y = next(iter(train_datatest))
    print(f"Input shape: {x.shape}")
    
    log_file = "matching_networks_training_log.txt"
    with open(log_file, 'w') as f:
        # 训练循环
        for epoch in range(300):
            matching_net.train()
            epoch_loss = 0
            batch_count = 0
            
            for batch in tqdm(train_datatest, desc=f'Epoch {epoch + 1}'):
                # Process batch for Matching Networks
                logits, query_labels = process_batch_matching(matching_net, batch, device, num_classes, num_support)
                
                # 计算损失
                loss = matching_loss(logits, query_labels)
                
                # 反向传播和优化
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                epoch_loss += loss.item()
                batch_count += 1
            
            avg_loss = epoch_loss / batch_count
            print(f'Epoch {epoch + 1}, Average Loss: {avg_loss:.4f}')
            
            # 测试部分
            test_accuracy = evaluate_matching_network(matching_net, test_datatest, device, num_classes, num_support)
            print(f'Epoch {epoch + 1}, Test Accuracy: {test_accuracy:.2f}%')
            
            f.write(f'Epoch {epoch + 1}, Loss: {avg_loss:.4f}, Accuracy: {test_accuracy:.2f}%\n')
    
    # 保存模型
    torch.save(matching_net.state_dict(), 'matching_network_model.pth')
    print("Training log saved to:", log_file)
    
    # 最终测试和混淆矩阵
    matching_net.eval()
    with torch.no_grad():
        final_test_correct = 0
        final_test_total = 0
        all_labels = []
        all_preds = []
        
        for batch in test_datatest:
            logits, query_labels = process_batch_matching(matching_net, batch, device, num_classes, num_support)
            
            _, predicted = torch.max(logits, dim=1)
            final_test_total += query_labels.size(0)
            final_test_correct += (predicted == query_labels).sum().item()
            
            all_labels.extend(query_labels.cpu().numpy())
            all_preds.extend(predicted.cpu().numpy())
        
        # 计算最终测试准确率
        final_test_accuracy = 100 * final_test_correct / final_test_total
        print(f'Final Test Accuracy: {final_test_accuracy:.2f}%')
        
        # 生成混淆矩阵
        cm = confusion_matrix(all_labels, all_preds)
        disp = ConfusionMatrixDisplay(confusion_matrix=cm)
        disp.plot(cmap=plt.cm.Blues)
        plt.title('Matching Networks - Confusion Matrix')
        plt.show()


if __name__ == '__main__':
    main()

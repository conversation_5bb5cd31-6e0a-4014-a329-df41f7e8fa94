import random
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from scipy.io import loadmat
from sklearn.model_selection import train_test_split
from torch import optim
from torch.utils.data import DataLoader
from data import CustomDataset
import matplotlib.pyplot as plt
from tqdm import tqdm
import time

# Import both models
from Test import PrototypicalNetwork, compute_prototypes, prototypical_loss, process_batch
from MatchingNetworks import MatchingNetwork, matching_loss, process_batch_matching, evaluate_matching_network

def set_seed(seed):
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def evaluate_prototypical_network(proto_net, test_loader, device, num_classes, num_support):
    """Evaluate Prototypical Network on test set"""
    proto_net.eval()
    correct = 0
    total = 0
    
    with torch.no_grad():
        for batch in test_loader:
            prototypes, query_embeddings, query_labels = process_batch(proto_net, batch, device, num_classes, num_support)
            distances = torch.cdist(query_embeddings, prototypes)
            _, predicted = torch.min(distances, dim=1)
            
            total += query_labels.size(0)
            correct += (predicted == query_labels).sum().item()
    
    accuracy = 100 * correct / total
    return accuracy


def train_and_evaluate_model(model_type, model, optimizer, train_loader, test_loader, device, num_classes, num_support, epochs=50):
    """Train and evaluate a model"""
    train_losses = []
    test_accuracies = []
    training_times = []
    
    print(f"\nTraining {model_type}...")
    
    for epoch in range(epochs):
        start_time = time.time()
        model.train()
        epoch_loss = 0
        batch_count = 0
        
        for batch in tqdm(train_loader, desc=f'{model_type} Epoch {epoch + 1}'):
            if model_type == "Prototypical Network":
                prototypes, query_embeddings, query_labels = process_batch(model, batch, device, num_classes, num_support)
                loss = prototypical_loss(prototypes, query_embeddings, query_labels)
            else:  # Matching Network
                logits, query_labels = process_batch_matching(model, batch, device, num_classes, num_support)
                loss = matching_loss(logits, query_labels)
            
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            epoch_loss += loss.item()
            batch_count += 1
        
        avg_loss = epoch_loss / batch_count
        train_losses.append(avg_loss)
        
        # Evaluate on test set
        if model_type == "Prototypical Network":
            test_accuracy = evaluate_prototypical_network(model, test_loader, device, num_classes, num_support)
        else:  # Matching Network
            test_accuracy = evaluate_matching_network(model, test_loader, device, num_classes, num_support)
        
        test_accuracies.append(test_accuracy)
        
        epoch_time = time.time() - start_time
        training_times.append(epoch_time)
        
        if (epoch + 1) % 10 == 0:
            print(f'{model_type} - Epoch {epoch + 1}, Loss: {avg_loss:.4f}, Accuracy: {test_accuracy:.2f}%, Time: {epoch_time:.2f}s')
    
    return train_losses, test_accuracies, training_times


def plot_comparison(proto_losses, proto_accuracies, proto_times, 
                   matching_losses, matching_accuracies, matching_times, epochs):
    """Plot comparison between the two models"""
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # Training Loss Comparison
    ax1.plot(range(1, epochs + 1), proto_losses, label='Prototypical Network', color='blue')
    ax1.plot(range(1, epochs + 1), matching_losses, label='Matching Network', color='red')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Training Loss')
    ax1.set_title('Training Loss Comparison')
    ax1.legend()
    ax1.grid(True)
    
    # Test Accuracy Comparison
    ax2.plot(range(1, epochs + 1), proto_accuracies, label='Prototypical Network', color='blue')
    ax2.plot(range(1, epochs + 1), matching_accuracies, label='Matching Network', color='red')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Test Accuracy (%)')
    ax2.set_title('Test Accuracy Comparison')
    ax2.legend()
    ax2.grid(True)
    
    # Training Time per Epoch
    ax3.plot(range(1, epochs + 1), proto_times, label='Prototypical Network', color='blue')
    ax3.plot(range(1, epochs + 1), matching_times, label='Matching Network', color='red')
    ax3.set_xlabel('Epoch')
    ax3.set_ylabel('Time per Epoch (seconds)')
    ax3.set_title('Training Time per Epoch')
    ax3.legend()
    ax3.grid(True)
    
    # Final Performance Summary
    final_proto_acc = proto_accuracies[-1]
    final_matching_acc = matching_accuracies[-1]
    avg_proto_time = np.mean(proto_times)
    avg_matching_time = np.mean(matching_times)
    
    ax4.bar(['Prototypical\nNetwork', 'Matching\nNetwork'], 
            [final_proto_acc, final_matching_acc], 
            color=['blue', 'red'], alpha=0.7)
    ax4.set_ylabel('Final Test Accuracy (%)')
    ax4.set_title('Final Performance Comparison')
    ax4.grid(True, axis='y')
    
    # Add text annotations
    ax4.text(0, final_proto_acc + 1, f'{final_proto_acc:.2f}%', ha='center', va='bottom')
    ax4.text(1, final_matching_acc + 1, f'{final_matching_acc:.2f}%', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('model_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Print summary statistics
    print("\n" + "="*60)
    print("COMPARISON SUMMARY")
    print("="*60)
    print(f"{'Metric':<25} {'Prototypical':<15} {'Matching':<15} {'Winner':<10}")
    print("-"*60)
    print(f"{'Final Accuracy (%)':<25} {final_proto_acc:<15.2f} {final_matching_acc:<15.2f} {'Proto' if final_proto_acc > final_matching_acc else 'Matching':<10}")
    print(f"{'Best Accuracy (%)':<25} {max(proto_accuracies):<15.2f} {max(matching_accuracies):<15.2f} {'Proto' if max(proto_accuracies) > max(matching_accuracies) else 'Matching':<10}")
    print(f"{'Final Loss':<25} {proto_losses[-1]:<15.4f} {matching_losses[-1]:<15.4f} {'Proto' if proto_losses[-1] < matching_losses[-1] else 'Matching':<10}")
    print(f"{'Avg Time/Epoch (s)':<25} {avg_proto_time:<15.2f} {avg_matching_time:<15.2f} {'Proto' if avg_proto_time < avg_matching_time else 'Matching':<10}")
    print("="*60)


def main():
    seed = 42
    set_seed(seed)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 数据集参数
    num_classes = 10
    num_support = 10
    num_query = 15
    epochs = 50  # Reduced for comparison
    
    # Load data
    dictdata = loadmat("traindata_5dB.mat")
    dataload = dictdata['traindata']
    
    # 网络参数
    input_size = 8
    hidden_size = 256
    output_size = 64
    
    # 创建数据加载器
    dataset = CustomDataset(dataload, noisy=False)
    train_dataset, test_dataset = train_test_split(dataset, test_size=0.9, random_state=42)
    
    train_loader = DataLoader(train_dataset, batch_size=num_classes * (num_support + num_query), shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=num_classes * (num_support + num_query), shuffle=True)
    
    print(f"Train batches: {len(train_loader)}, Test batches: {len(test_loader)}")
    
    # Initialize models
    proto_net = PrototypicalNetwork(input_size, hidden_size, output_size)
    matching_net = MatchingNetwork(input_size, hidden_size, output_size)
    
    proto_net.to(device)
    matching_net.to(device)
    
    # Initialize optimizers
    proto_optimizer = optim.Adam(proto_net.parameters(), lr=0.001)
    matching_optimizer = optim.Adam(matching_net.parameters(), lr=0.001)
    
    # Train and evaluate both models
    proto_losses, proto_accuracies, proto_times = train_and_evaluate_model(
        "Prototypical Network", proto_net, proto_optimizer, train_loader, test_loader, 
        device, num_classes, num_support, epochs
    )
    
    matching_losses, matching_accuracies, matching_times = train_and_evaluate_model(
        "Matching Network", matching_net, matching_optimizer, train_loader, test_loader, 
        device, num_classes, num_support, epochs
    )
    
    # Plot comparison
    plot_comparison(proto_losses, proto_accuracies, proto_times,
                   matching_losses, matching_accuracies, matching_times, epochs)
    
    # Save models
    torch.save(proto_net.state_dict(), 'prototypical_network_comparison.pth')
    torch.save(matching_net.state_dict(), 'matching_network_comparison.pth')
    
    print("\nModels saved successfully!")
    print("Comparison plot saved as 'model_comparison.png'")


if __name__ == '__main__':
    main()

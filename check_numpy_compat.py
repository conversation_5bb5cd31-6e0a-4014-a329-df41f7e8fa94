import numpy as np
import pkg_resources
import sys

print(f"✅ 当前 NumPy 版本: {np.__version__}\n")

# 库列表（可按需添加）
libraries_to_check = {
    'matplotlib': '3.8.4',
    'scipy': '1.13.0',
    'pandas': '2.2.2',
    'scikit-learn': '1.4.2',
    'seaborn': '0.13.2',
    'xgboost': '2.0.3',
    'statsmodels': '0.14.1'
}

incompatible = []

for lib, min_version in libraries_to_check.items():
    try:
        installed_version = pkg_resources.get_distribution(lib).version
        print(f"🔍 {lib}: 已安装版本 {installed_version}")

        if np.__version__.startswith('2') and pkg_resources.parse_version(
                installed_version) < pkg_resources.parse_version(min_version):
            incompatible.append((lib, installed_version, min_version))
    except pkg_resources.DistributionNotFound:
        print(f"⚠️ {lib} 未安装")

if incompatible:
    print("\n❌ 发现以下库的版本可能与 NumPy 2 不兼容：\n")
    for lib, current, required in incompatible:
        print(f"  - {lib}: 当前版本 {current}，建议升级到 ≥ {required}")
    print("\n✅ 建议操作：")
    print("  - 升级这些库：")
    print("    pip install --upgrade " + " ".join(lib for lib, _, _ in incompatible))
    print("  - 或者降级 NumPy：")
    print("    pip install 'numpy<2'")
else:
    print("\n✅ 没有发现明显不兼容的库。你的环境应该可以运行 NumPy 2。")

print("\n🎯 提示：你也可以创建新的虚拟环境来避免版本冲突。")

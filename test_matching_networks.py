import torch
import numpy as np
from scipy.io import loadmat
from sklearn.model_selection import train_test_split
from torch.utils.data import DataLoader
from data import CustomDataset
from MatchingNetworks import MatchingNetwork, matching_loss, process_batch_matching

def test_matching_networks():
    """Test Matching Networks implementation with your data format"""
    
    print("Testing Matching Networks implementation...")
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Parameters (same as your PrototypicalNetwork)
    num_classes = 10
    num_support = 10
    num_query = 15
    input_size = 8
    hidden_size = 256
    output_size = 64
    
    # Load data
    try:
        dictdata = loadmat("traindata_5dB.mat")
        dataload = dictdata['traindata']
        print(f"Data loaded successfully. Shape: {dataload.shape}")
    except FileNotFoundError:
        print("traindata_5dB.mat not found. Creating dummy data for testing...")
        # Create dummy data with same format as your real data
        dataload = np.random.randn(1000, 9)  # 8 features + 1 label
        dataload[:, -1] = np.random.randint(0, num_classes, 1000)  # Random labels
    
    # Create dataset and dataloader
    dataset = CustomDataset(dataload, noisy=False)
    train_dataset, test_dataset = train_test_split(dataset, test_size=0.3, random_state=42)
    
    batch_size = num_classes * (num_support + num_query)
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=True)
    
    print(f"Dataset created. Train size: {len(train_dataset)}, Test size: {len(test_dataset)}")
    print(f"Batch size: {batch_size}")
    
    # Initialize model
    matching_net = MatchingNetwork(input_size, hidden_size, output_size)
    matching_net.to(device)
    
    print(f"Model initialized. Total parameters: {sum(p.numel() for p in matching_net.parameters())}")
    
    # Test forward pass
    try:
        batch = next(iter(train_loader))
        print(f"Batch shapes - Input: {batch[0].shape}, Labels: {batch[1].shape}")
        
        # Test batch processing
        logits, query_labels = process_batch_matching(matching_net, batch, device, num_classes, num_support)
        print(f"Forward pass successful!")
        print(f"Logits shape: {logits.shape}, Query labels shape: {query_labels.shape}")
        
        # Test loss computation
        loss = matching_loss(logits, query_labels)
        print(f"Loss computed: {loss.item():.4f}")
        
        # Test prediction
        _, predicted = torch.max(logits, dim=1)
        accuracy = (predicted == query_labels).float().mean().item() * 100
        print(f"Random accuracy: {accuracy:.2f}%")
        
        print("\n✅ All tests passed! Matching Networks implementation is working correctly.")
        
        # Test a few training steps
        print("\nTesting training loop...")
        optimizer = torch.optim.Adam(matching_net.parameters(), lr=0.001)
        
        matching_net.train()
        for i, batch in enumerate(train_loader):
            if i >= 3:  # Test only 3 batches
                break
                
            logits, query_labels = process_batch_matching(matching_net, batch, device, num_classes, num_support)
            loss = matching_loss(logits, query_labels)
            
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            _, predicted = torch.max(logits, dim=1)
            accuracy = (predicted == query_labels).float().mean().item() * 100
            
            print(f"Batch {i+1}: Loss = {loss.item():.4f}, Accuracy = {accuracy:.2f}%")
        
        print("\n✅ Training loop test passed!")
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == '__main__':
    success = test_matching_networks()
    if success:
        print("\n🎉 Matching Networks is ready for comparison with your PrototypicalNetwork!")
        print("\nNext steps:")
        print("1. Run 'python MatchingNetworks.py' to train Matching Networks")
        print("2. Run 'python compare_models.py' to compare both models")
    else:
        print("\n❌ Please fix the issues before proceeding.")
